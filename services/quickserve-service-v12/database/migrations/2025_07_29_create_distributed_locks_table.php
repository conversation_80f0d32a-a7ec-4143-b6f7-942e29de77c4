<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('distributed_locks', function (Blueprint $table) {
            $table->id();
            $table->string('lock_key', 255)->unique()->index();
            $table->string('lock_id', 255);
            $table->timestamp('expires_at')->index();
            $table->timestamps();
            
            // Index for cleanup of expired locks
            $table->index(['expires_at', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('distributed_locks');
    }
};
