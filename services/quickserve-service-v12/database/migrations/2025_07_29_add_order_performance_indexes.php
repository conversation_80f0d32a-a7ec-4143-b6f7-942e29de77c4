<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add performance indexes for order creation optimization
        
        // Index for orders table - critical for order creation performance
        Schema::table('orders', function (Blueprint $table) {
            // Composite index for order deduplication checks
            $table->index(['order_no', 'order_date'], 'idx_orders_no_date');
            
            // Index for customer-based order lookups
            $table->index(['customer_code', 'order_date', 'created_date'], 'idx_orders_customer_date_created');
            
            // Index for order status queries
            $table->index(['order_status', 'order_date'], 'idx_orders_status_date');
        });

        // Index for order_details table - improves order details creation
        Schema::table('order_details', function (Blueprint $table) {
            // Composite index for order details lookups
            $table->index(['ref_order_no', 'order_date'], 'idx_order_details_ref_date');
            
            // Index for meal code lookups
            $table->index(['meal_code', 'order_date'], 'idx_order_details_meal_date');
        });

        // Index for temp_pre_orders table - improves payment callback performance
        Schema::table('temp_pre_orders', function (Blueprint $table) {
            // Composite index for related order lookups
            $table->index(['customer_code', 'order_date', 'last_modified'], 'idx_temp_orders_customer_date_modified');
            
            // Index for order number lookups
            $table->index(['order_no', 'order_date'], 'idx_temp_orders_no_date');
        });

        // Index for payment_transaction table - improves payment processing
        Schema::table('payment_transaction', function (Blueprint $table) {
            // Index for transaction status updates
            $table->index(['order_no', 'status'], 'idx_payment_txn_order_status');
            
            // Index for payment service transaction lookups
            $table->index(['payment_service_transaction_id'], 'idx_payment_txn_service_id');
        });

        // Index for temp_order_payment table - improves payment status updates
        Schema::table('temp_order_payment', function (Blueprint $table) {
            // Index for temp order payment lookups
            $table->index(['temp_pre_order_id', 'payment_status'], 'idx_temp_payment_order_status');
        });

        // Index for product_planner table - improves meal item lookups
        Schema::table('product_planner', function (Blueprint $table) {
            // Composite index for meal planning queries
            $table->index(['date', 'menu', 'fk_kitchen_code', 'generic_product_code'], 'idx_planner_date_menu_kitchen_product');
        });

        // Index for products table - improves product lookups
        Schema::table('products', function (Blueprint $table) {
            // Index for product status and code lookups
            $table->index(['pk_product_code', 'status'], 'idx_products_code_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the performance indexes
        
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('idx_orders_no_date');
            $table->dropIndex('idx_orders_customer_date_created');
            $table->dropIndex('idx_orders_status_date');
        });

        Schema::table('order_details', function (Blueprint $table) {
            $table->dropIndex('idx_order_details_ref_date');
            $table->dropIndex('idx_order_details_meal_date');
        });

        Schema::table('temp_pre_orders', function (Blueprint $table) {
            $table->dropIndex('idx_temp_orders_customer_date_modified');
            $table->dropIndex('idx_temp_orders_no_date');
        });

        Schema::table('payment_transaction', function (Blueprint $table) {
            $table->dropIndex('idx_payment_txn_order_status');
            $table->dropIndex('idx_payment_txn_service_id');
        });

        Schema::table('temp_order_payment', function (Blueprint $table) {
            $table->dropIndex('idx_temp_payment_order_status');
        });

        Schema::table('product_planner', function (Blueprint $table) {
            $table->dropIndex('idx_planner_date_menu_kitchen_product');
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex('idx_products_code_status');
        });
    }
};
