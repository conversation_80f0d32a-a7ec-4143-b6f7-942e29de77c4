#!/usr/bin/env php
<?php

/**
 * Test script to verify the payment timeout fix
 * This script tests the complete payment flow to ensure no timeouts occur
 */

echo "🧪 Testing Payment Service Timeout Fix\n";
echo str_repeat("=", 60) . "\n\n";

// Configuration
$paymentServiceUrl = 'http://*************:8002';
$quickserveServiceUrl = 'http://*************:8003';

// Test data
$testOrderData = [
    'customer_id' => 1,
    'customer_name' => 'Test Customer',
    'customer_email' => '<EMAIL>',
    'customer_phone' => '+918450940705',
    'customer_address' => 'Test Address',
    'location_code' => 1,
    'location_name' => 'Test Location',
    'subscription_days' => 5,
    'selected_days' => [1, 2, 3, 4, 5],
    'delivery_time' => '08:00:00',
    'delivery_end_time' => '10:00:00',
    'meals' => [
        [
            'meal_type' => 'breakfast',
            'items' => [
                [
                    'item_id' => 1,
                    'item_name' => 'Test Breakfast',
                    'quantity' => 1,
                    'price' => 50.00
                ]
            ]
        ]
    ]
];

function makeHttpRequest($url, $data = null, $method = 'GET', $timeout = 30) {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => $timeout,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Accept: application/json'
        ]
    ]);
    
    if ($method === 'POST' && $data) {
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $startTime = microtime(true);
    $response = curl_exec($ch);
    $duration = microtime(true) - $startTime;
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'success' => $response !== false && empty($error),
        'response' => $response,
        'http_code' => $httpCode,
        'duration' => round($duration, 2),
        'error' => $error
    ];
}

// Step 1: Test Order Creation
echo "Step 1: Testing Order Creation...\n";
$orderResult = makeHttpRequest(
    $quickserveServiceUrl . '/api/v2/order-management/create-order',
    $testOrderData,
    'POST',
    60 // Increased timeout
);

if (!$orderResult['success']) {
    echo "❌ Order creation failed: " . $orderResult['error'] . "\n";
    exit(1);
}

$orderResponse = json_decode($orderResult['response'], true);
if (!$orderResponse || !$orderResponse['success']) {
    echo "❌ Order creation failed: " . ($orderResponse['message'] ?? 'Unknown error') . "\n";
    exit(1);
}

$orderNo = $orderResponse['data']['order_no'];
$transactionId = $orderResponse['data']['payment_service_transaction_id'];

echo "✅ Order created successfully in {$orderResult['duration']}s\n";
echo "   Order No: {$orderNo}\n";
echo "   Transaction ID: {$transactionId}\n\n";

// Step 2: Test Payment Success Callback (This is where the timeout was occurring)
echo "Step 2: Testing Payment Success Callback...\n";
$paymentData = [
    'payment_service_transaction_id' => $transactionId,
    'gateway' => 'razorpay',
    'amount' => 50.00,
    'status' => 'completed'
];

$paymentResult = makeHttpRequest(
    $quickserveServiceUrl . "/api/v2/order-management/payment-success/{$orderNo}",
    $paymentData,
    'POST',
    90 // Increased timeout for this critical test
);

if (!$paymentResult['success']) {
    echo "❌ Payment callback failed: " . $paymentResult['error'] . "\n";
    echo "   Duration before timeout: {$paymentResult['duration']}s\n";
    exit(1);
}

$paymentResponse = json_decode($paymentResult['response'], true);
if (!$paymentResponse || !$paymentResponse['success']) {
    echo "❌ Payment callback failed: " . ($paymentResponse['message'] ?? 'Unknown error') . "\n";
    exit(1);
}

echo "✅ Payment callback processed successfully in {$paymentResult['duration']}s\n";
echo "   Status: " . ($paymentResponse['data']['status'] ?? 'completed') . "\n";
echo "   Orders being created: " . ($paymentResponse['data']['related_temp_orders_count'] ?? 'N/A') . "\n\n";

// Step 3: Verify Order Status
echo "Step 3: Verifying Order Status...\n";
sleep(2); // Wait for async processing

$statusResult = makeHttpRequest(
    $quickserveServiceUrl . "/api/v2/order-management/pre-order-status/{$orderNo}",
    null,
    'GET',
    30
);

if ($statusResult['success']) {
    $statusResponse = json_decode($statusResult['response'], true);
    echo "✅ Order status retrieved in {$statusResult['duration']}s\n";
    if ($statusResponse && isset($statusResponse['data'])) {
        echo "   Payment Status: " . ($statusResponse['data']['payment_status'] ?? 'N/A') . "\n";
        echo "   Order Status: " . ($statusResponse['data']['order_status'] ?? 'N/A') . "\n";
    }
} else {
    echo "⚠️  Could not retrieve order status: " . $statusResult['error'] . "\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 TIMEOUT FIX TEST COMPLETED SUCCESSFULLY!\n";
echo "\n📊 Performance Summary:\n";
echo "   - Order Creation: {$orderResult['duration']}s\n";
echo "   - Payment Callback: {$paymentResult['duration']}s (Previously timed out at 30s)\n";
echo "   - Status Check: " . ($statusResult['duration'] ?? 'N/A') . "s\n";
echo "\n✨ The timeout issue has been resolved!\n";

?>
