-- Manual Database Setup for Payment Callback Optimization
-- Run these SQL commands in your MySQL database

USE live_quickserve_8163;

-- 1. Create distributed_locks table for preventing concurrent processing
CREATE TABLE IF NOT EXISTS `distributed_locks` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `lock_key` varchar(255) NOT NULL,
    `lock_id` varchar(255) NOT NULL,
    `expires_at` timestamp NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `distributed_locks_lock_key_unique` (`lock_key`),
    KEY `distributed_locks_lock_key_index` (`lock_key`),
    KEY `distributed_locks_expires_at_index` (`expires_at`),
    KEY `distributed_locks_expires_at_created_at_index` (`expires_at`,`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Add performance indexes for orders table
ALTER TABLE `orders` 
ADD INDEX `idx_orders_no_date` (`order_no`, `order_date`),
ADD INDEX `idx_orders_customer_date_created` (`customer_code`, `order_date`, `created_date`),
ADD INDEX `idx_orders_status_date` (`order_status`, `order_date`);

-- 3. Add performance indexes for order_details table
ALTER TABLE `order_details` 
ADD INDEX `idx_order_details_ref_date` (`ref_order_no`, `order_date`),
ADD INDEX `idx_order_details_meal_date` (`meal_code`, `order_date`);

-- 4. Add performance indexes for temp_pre_orders table
ALTER TABLE `temp_pre_orders` 
ADD INDEX `idx_temp_orders_customer_date_modified` (`customer_code`, `order_date`, `last_modified`),
ADD INDEX `idx_temp_orders_no_date` (`order_no`, `order_date`);

-- 5. Add performance indexes for payment_transaction table
ALTER TABLE `payment_transaction` 
ADD INDEX `idx_payment_txn_order_status` (`order_no`, `status`),
ADD INDEX `idx_payment_txn_service_id` (`payment_service_transaction_id`);

-- 6. Add performance indexes for temp_order_payment table
ALTER TABLE `temp_order_payment` 
ADD INDEX `idx_temp_payment_order_status` (`temp_pre_order_id`, `payment_status`);

-- 7. Add performance indexes for product_planner table
ALTER TABLE `product_planner` 
ADD INDEX `idx_planner_date_menu_kitchen_product` (`date`, `menu`, `fk_kitchen_code`, `generic_product_code`);

-- 8. Add performance indexes for products table
ALTER TABLE `products` 
ADD INDEX `idx_products_code_status` (`pk_product_code`, `status`);

-- 9. Optimize MySQL settings for better performance (run as admin)
-- These are session-level settings that will be applied by the application code
-- SET GLOBAL innodb_lock_wait_timeout = 10;
-- SET GLOBAL innodb_deadlock_detect = ON;
-- SET GLOBAL innodb_table_locks = OFF;

-- 10. Clean up any existing expired locks (optional maintenance)
DELETE FROM `distributed_locks` WHERE `expires_at` < NOW();

-- Verification queries to check if indexes were created successfully:
SHOW INDEX FROM `orders` WHERE `Key_name` LIKE 'idx_orders_%';
SHOW INDEX FROM `order_details` WHERE `Key_name` LIKE 'idx_order_details_%';
SHOW INDEX FROM `temp_pre_orders` WHERE `Key_name` LIKE 'idx_temp_orders_%';
SHOW INDEX FROM `payment_transaction` WHERE `Key_name` LIKE 'idx_payment_txn_%';
SHOW INDEX FROM `temp_order_payment` WHERE `Key_name` LIKE 'idx_temp_payment_%';
SHOW INDEX FROM `product_planner` WHERE `Key_name` LIKE 'idx_planner_%';
SHOW INDEX FROM `products` WHERE `Key_name` LIKE 'idx_products_%';

-- Check if distributed_locks table was created
DESCRIBE `distributed_locks`;