#!/bin/bash

echo "🚀 Testing Payment Callback Optimization"
echo "========================================"
echo ""

# Test configuration
QUICKSERVE_URL="http://10.145.135.127:8003"
PAYMENT_URL="http://10.145.135.127:8002"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to log with timestamp
log_with_time() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

# Function to measure execution time
measure_time() {
    local start_time=$(date +%s.%N)
    eval "$1"
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc -l)
    printf "%.3f" $duration
}

echo "Step 1: Creating test order..."
log_with_time "Creating order with products 342 and 343"

ORDER_RESPONSE=$(curl -s -X 'POST' \
  "${QUICKSERVE_URL}/api/v2/order-management/create" \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "customer_id": 1,
  "customer_address": "123 Test Street, Test Area, Test City - 400001",
  "location_code": 1001,
  "location_name": "Test Location",
  "city": 1,
  "city_name": "Mumbai",
  "meals": [
    {
      "product_code": 342,
      "quantity": 1
    },
    {
      "product_code": 343,
      "quantity": 1
    }
  ],
  "start_date": "2025-07-30",
  "selected_days": [1,2,3,4,5],
  "delivery_time": "08:00:00",
  "delivery_end_time": "09:00:00",
  "food_preference": "veg",
  "subscription_days": 1
}')

# Extract transaction ID and order number
TRANSACTION_ID=$(echo "$ORDER_RESPONSE" | grep -o '"payment_service_transaction_id":"[^"]*"' | cut -d'"' -f4)
ORDER_NO=$(echo "$ORDER_RESPONSE" | grep -o '"order_no":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TRANSACTION_ID" ] || [ -z "$ORDER_NO" ]; then
    echo -e "${RED}❌ Failed to create order or extract transaction details${NC}"
    echo "Response: $ORDER_RESPONSE"
    exit 1
fi

echo -e "${GREEN}✅ Order created successfully${NC}"
echo "   Transaction ID: $TRANSACTION_ID"
echo "   Order No: $ORDER_NO"
echo ""

echo "Step 2: Processing payment..."
log_with_time "Initiating payment processing"

PAYMENT_RESPONSE=$(curl -s --location "${PAYMENT_URL}/api/v2/payments/${TRANSACTION_ID}/process" \
--header 'Content-Type: application/json' \
--header 'Accept: application/json' \
--data '{
    "gateway": "razorpay"
}')

RAZORPAY_ORDER_ID=$(echo "$PAYMENT_RESPONSE" | grep -o '"razorpay_order_id":"[^"]*"' | cut -d'"' -f4)

if [ -z "$RAZORPAY_ORDER_ID" ]; then
    echo -e "${RED}❌ Failed to process payment${NC}"
    echo "Response: $PAYMENT_RESPONSE"
    exit 1
fi

echo -e "${GREEN}✅ Payment processing initiated${NC}"
echo "   Razorpay Order ID: $RAZORPAY_ORDER_ID"
echo ""

echo "Step 3: Testing payment callback (This is the critical test)..."
log_with_time "Starting payment callback test"

# Measure callback execution time
echo -n "   Executing callback: "
CALLBACK_TIME=$(measure_time "
CALLBACK_RESPONSE=\$(curl -s --location '${PAYMENT_URL}/api/v2/payments/callback' \
--header 'Content-Type: application/json' \
--header 'Accept: application/json' \
--data '{
    \"razorpay_payment_id\": \"pay_test_$(date +%s)\",
    \"razorpay_order_id\": \"${RAZORPAY_ORDER_ID}\",
    \"razorpay_signature\": \"test_signature_$(date +%s)\",
    \"transaction_id\": \"${TRANSACTION_ID}\"
}')
")

echo "${CALLBACK_TIME}s"

# Check if callback was successful
if echo "$CALLBACK_RESPONSE" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ Payment callback completed successfully${NC}"
    echo "   Duration: ${CALLBACK_TIME}s (Previously: 53+ seconds)"
    
    if (( $(echo "$CALLBACK_TIME < 5.0" | bc -l) )); then
        echo -e "${GREEN}🎉 EXCELLENT: Callback completed in under 5 seconds!${NC}"
    elif (( $(echo "$CALLBACK_TIME < 15.0" | bc -l) )); then
        echo -e "${YELLOW}✅ GOOD: Callback completed in under 15 seconds${NC}"
    else
        echo -e "${YELLOW}⚠️  IMPROVED: Still faster than before but could be better${NC}"
    fi
else
    echo -e "${RED}❌ Payment callback failed${NC}"
    echo "Response: $CALLBACK_RESPONSE"
fi

echo ""
echo "Step 4: Verifying order creation..."
log_with_time "Checking if orders were created in database"

# Wait a moment for async processing
sleep 3

# Check order status (this endpoint might not exist, so we'll just log the attempt)
STATUS_RESPONSE=$(curl -s "${QUICKSERVE_URL}/api/v2/order-management/pre-order-status/${ORDER_NO}" 2>/dev/null || echo "Status endpoint not available")

if echo "$STATUS_RESPONSE" | grep -q '"success":true'; then
    echo -e "${GREEN}✅ Order status retrieved successfully${NC}"
    PAYMENT_STATUS=$(echo "$STATUS_RESPONSE" | grep -o '"payment_status":"[^"]*"' | cut -d'"' -f4)
    echo "   Payment Status: ${PAYMENT_STATUS:-'N/A'}"
else
    echo -e "${YELLOW}⚠️  Order status check not available or failed${NC}"
fi

echo ""
echo "========================================"
echo -e "${GREEN}🎯 OPTIMIZATION TEST RESULTS${NC}"
echo "========================================"
echo ""
echo "📊 Performance Comparison:"
echo "   Before Optimization: 53+ seconds (timeout)"
echo "   After Optimization:  ${CALLBACK_TIME}s"
echo ""

if (( $(echo "$CALLBACK_TIME < 10.0" | bc -l) )); then
    IMPROVEMENT=$(echo "scale=1; 53 / $CALLBACK_TIME" | bc -l)
    echo -e "${GREEN}✨ SUCCESS: ${IMPROVEMENT}x faster than before!${NC}"
    echo ""
    echo "🔧 Optimizations Applied:"
    echo "   ✅ Async order processing with immediate response"
    echo "   ✅ Database lock timeout reduced (50s → 10s)"
    echo "   ✅ Retry logic with exponential backoff"
    echo "   ✅ Batch database operations"
    echo "   ✅ Optimized MySQL session settings"
    echo "   ✅ HTTP client timeout increased (10s → 60s)"
    echo ""
    echo -e "${GREEN}🎉 PAYMENT TIMEOUT ISSUE RESOLVED!${NC}"
else
    echo -e "${YELLOW}⚠️  Partial improvement achieved${NC}"
    echo "   Further optimization may be needed"
fi

echo ""
echo "📝 Next Steps:"
echo "   1. Monitor production performance"
echo "   2. Check database indexes on orders table"
echo "   3. Consider implementing Laravel queues for heavy operations"
echo "   4. Add database connection pooling if needed"
echo ""
